import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_selector.dart';

class MemberDepartmentPage extends StatefulWidget {
  const MemberDepartmentPage({super.key});

  @override
  State<MemberDepartmentPage> createState() => _MemberDepartmentPageState();
}

class _MemberDepartmentPageState extends State<MemberDepartmentPage> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController depNameController = TextEditingController();

  /// 打开添加部门弹窗
  void _showAddDepartmentDrawer(BuildContext context) {
    bool isAddNext = false;

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppInput(
              label: "部门名称",
              labelPosition: LabelPosition.left,
              hintText: "部门名称",
              size: InputSize.medium,
              controller: depNameController,
              maxLength: 30,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入部门名称';
                }
                return null;
              },
              onChanged: (value) {},
            ),
            //TODO:部门添加功能表单
            SizedBox(height: 30, child: Text('上级部门')),
            SizedBox(height: 30, child: Text('部门负责人')),
            SizedBox(height: 30, child: Text('部门HRBP')),
            AppInput(
              label: "职能描述",
              labelPosition: LabelPosition.left,
              maxLines: 5,
              hintText: "职能描述",
              size: InputSize.medium,
              maxLength: 3000,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Checkbox(
            value: isAddNext,
            onChanged: (value) {
              setState(() {
                isAddNext = !isAddNext;
              });
            },
          ),
          Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(
            text: '确定',
            type: ButtonType.primary,
            onPressed: () {
              if (!formKey.currentState!.validate()) return;

              context.pop();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(width: 400, child: DepartmentSelector()),
        Center(
          child: AppButton(
            text: '添加部门',
            type: ButtonType.primary,
            onPressed: () {
              _showAddDepartmentDrawer(context);
            },
          ),
        ),
      ],
    );
  }
}
